import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  LinearProgress,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  PlayArrow,
  CheckCircle,
  ArrowBack,
  Star,
  AccessTime,
  VideoLibrary
} from '@mui/icons-material';
// import { useAuth } from '../contexts/AuthContext';

const CourseViewer = ({ course, onBack }) => {
  // const { user } = useAuth(); // سيتم استخدامه لاحقاً
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [openVideoDialog, setOpenVideoDialog] = useState(false);
  const [courseProgress, setCourseProgress] = useState(null);

  useEffect(() => {
    if (course) {
      fetchCourseProgress();
    }
  }, [course]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchCourseProgress = async () => {
    try {
      // في التطبيق الحقيقي، ستجلب التقدم من الخادم
      setCourseProgress({
        completedVideos: course.completedVideos || 0,
        totalVideos: course.totalVideos || course.videos?.length || 0,
        progress: course.progress || 0
      });
    } catch (error) {
      console.error('خطأ في جلب تقدم الكورس:', error);
    }
  };

  const handleVideoClick = (video) => {
    setSelectedVideo(video);
    setOpenVideoDialog(true);
  };

  const markVideoAsCompleted = async (videoId) => {
    try {
      // في التطبيق الحقيقي، ستحدث الخادم
      console.log('تم إكمال الفيديو:', videoId);
      
      // تحديث محلي
      const updatedProgress = {
        ...courseProgress,
        completedVideos: courseProgress.completedVideos + 1
      };
      updatedProgress.progress = Math.round((updatedProgress.completedVideos / updatedProgress.totalVideos) * 100);
      setCourseProgress(updatedProgress);
      
      setOpenVideoDialog(false);
    } catch (error) {
      console.error('خطأ في تحديث التقدم:', error);
    }
  };

  if (!course) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6">لم يتم العثور على الكورس</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={onBack} sx={{ mr: 2 }}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          {course.title}
        </Typography>
      </Box>

      {/* Course Info */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {course.description}
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip 
                  icon={<Star />} 
                  label={`${course.rating || 4.5} ⭐`} 
                  size="small" 
                  color="warning"
                />
                <Chip 
                  icon={<AccessTime />} 
                  label={course.duration || '8 ساعات'} 
                  size="small" 
                />
                <Chip 
                  icon={<VideoLibrary />} 
                  label={`${course.totalVideos || course.videos?.length || 0} فيديو`} 
                  size="small" 
                />
                <Chip 
                  label={course.level || 'مبتدئ'} 
                  size="small"
                  color={course.level === 'متقدم' ? 'error' : course.level === 'متوسط' ? 'warning' : 'success'}
                />
              </Box>

              <Typography variant="subtitle2" color="textSecondary">
                المدرب: {course.instructor || 'علاء عبد الحميد'}
              </Typography>
            </Box>
          </Box>

          {/* Progress */}
          {courseProgress && (
            <Box sx={{ mt: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">
                  التقدم: {courseProgress.completedVideos}/{courseProgress.totalVideos}
                </Typography>
                <Typography variant="body2">
                  {courseProgress.progress}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={courseProgress.progress}
                sx={{ height: 8, borderRadius: 4 }}
                color={courseProgress.progress === 100 ? 'success' : 'primary'}
              />
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Videos List */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            محتوى الكورس
          </Typography>
          
          <List>
            {course.videos && course.videos.length > 0 ? (
              course.videos.map((video, index) => (
                <ListItem 
                  key={video.id || index}
                  sx={{ 
                    border: '1px solid #e0e0e0', 
                    borderRadius: 2, 
                    mb: 1,
                    '&:hover': { bgcolor: '#f5f5f5' }
                  }}
                >
                  <ListItemIcon>
                    {video.isCompleted ? (
                      <CheckCircle sx={{ color: '#4caf50' }} />
                    ) : (
                      <PlayArrow sx={{ color: '#2196f3' }} />
                    )}
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle1">
                          {video.title}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {video.duration}
                        </Typography>
                      </Box>
                    }
                    secondary={`الدرس ${index + 1}`}
                  />
                  
                  <Button
                    variant={video.isCompleted ? "outlined" : "contained"}
                    size="small"
                    startIcon={video.isCompleted ? <CheckCircle /> : <PlayArrow />}
                    onClick={() => handleVideoClick(video)}
                    sx={{ ml: 2 }}
                  >
                    {video.isCompleted ? 'مكتمل' : 'مشاهدة'}
                  </Button>
                </ListItem>
              ))
            ) : (
              <Typography variant="body2" color="textSecondary" sx={{ textAlign: 'center', py: 4 }}>
                لا توجد فيديوهات متاحة حالياً
              </Typography>
            )}
          </List>
        </CardContent>
      </Card>

      {/* Video Dialog */}
      <Dialog 
        open={openVideoDialog} 
        onClose={() => setOpenVideoDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedVideo?.title}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ 
            height: 300, 
            bgcolor: '#000', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            borderRadius: 2,
            mb: 2
          }}>
            <Typography variant="h6" sx={{ color: 'white' }}>
              🎥 مشغل الفيديو
            </Typography>
          </Box>
          
          <Typography variant="body1">
            مدة الفيديو: {selectedVideo?.duration}
          </Typography>
          
          <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
            في التطبيق الحقيقي، سيتم عرض الفيديو هنا
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenVideoDialog(false)}>
            إغلاق
          </Button>
          {selectedVideo && !selectedVideo.isCompleted && (
            <Button 
              variant="contained" 
              onClick={() => markVideoAsCompleted(selectedVideo.id)}
            >
              تم الانتهاء
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CourseViewer;
