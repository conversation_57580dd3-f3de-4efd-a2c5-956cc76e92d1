/**
 * خدمة التكامل الاحترافية بين Firebase و Supabase
 * Firebase: المصادقة، الاستضافة، التخزين، الإشعارات
 * Supabase: قاعدة البيانات، المزامنة الفورية، الاستعلامات المعقدة
 */

import { auth, db } from '../firebase/config';
import { supabase } from '../supabase/config';
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged
} from 'firebase/auth';
import {
  collection,
  addDoc,
  deleteDoc,
  doc,
  serverTimestamp,
  query,
  where,
  limit,
  getDocs,
  onSnapshot
} from 'firebase/firestore';

// استيراد خدمة التزامن الفوري الجديدة
import {
  realTimeCoursesSync,
  realTimeEnrollmentsSync,
  realTimeNotificationsSync
} from './realTimeSyncService';

/**
 * خدمة المصادقة المختلطة (Firebase Auth + Supabase Sync)
 */
export class HybridAuthService {
  constructor() {
    this.currentUser = null;
    this.authListeners = new Set();
    this.setupAuthListener();
  }

  // إعداد مستمع المصادقة
  setupAuthListener() {
    onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        // مزامنة بيانات المستخدم مع Supabase
        await this.syncUserWithSupabase(firebaseUser);
        this.currentUser = firebaseUser;
      } else {
        this.currentUser = null;
        // تسجيل خروج من Supabase أيضاً
        await supabase.auth.signOut();
      }
      
      // إشعار جميع المستمعين
      this.authListeners.forEach(listener => listener(this.currentUser));
    });
  }

  // مزامنة بيانات المستخدم مع Supabase
  async syncUserWithSupabase(firebaseUser) {
    try {
      // تسجيل دخول في Supabase باستخدام Firebase token
      const token = await firebaseUser.getIdToken();
      console.log('🔄 مزامنة المستخدم مع Supabase...');
      
      // البحث عن المستخدم في Supabase
      const { data: existingUser, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('firebase_uid', firebaseUser.uid)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError;
      }

      if (!existingUser) {
        // إنشاء مستخدم جديد في Supabase
        const { data, error } = await supabase
          .from('users')
          .insert([{
            firebase_uid: firebaseUser.uid,
            name: firebaseUser.displayName || 'مستخدم جديد',
            email: firebaseUser.email,
            role: firebaseUser.email === 'ALAA <EMAIL>' ? 'admin' : 'student',
            is_active: true,
            last_login: new Date().toISOString()
          }])
          .select()
          .single();

        if (error) throw error;
        console.log('✅ تم إنشاء مستخدم جديد في Supabase:', data.id);
      } else {
        // تحديث آخر تسجيل دخول
        await supabase
          .from('users')
          .update({ last_login: new Date().toISOString() })
          .eq('firebase_uid', firebaseUser.uid);
      }

      console.log('✅ تم مزامنة المستخدم مع Supabase');
    } catch (error) {
      console.error('❌ خطأ في مزامنة المستخدم:', error);
    }
  }

  // تسجيل دخول
  async signIn(email, password) {
    try {
      console.log('🔐 تسجيل دخول...');
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return { success: true, user: userCredential.user };
    } catch (error) {
      console.error('❌ خطأ في تسجيل الدخول:', error);
      throw error;
    }
  }

  // إنشاء حساب جديد
  async signUp(email, password, userData = {}) {
    try {
      console.log('📝 إنشاء حساب جديد...');
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      
      // إضافة بيانات إضافية في Supabase
      if (userData.name || userData.phone) {
        await supabase
          .from('users')
          .update({
            name: userData.name,
            phone: userData.phone
          })
          .eq('firebase_uid', userCredential.user.uid);
      }

      return { success: true, user: userCredential.user };
    } catch (error) {
      console.error('❌ خطأ في إنشاء الحساب:', error);
      throw error;
    }
  }

  // تسجيل خروج
  async signOut() {
    try {
      console.log('🚪 تسجيل خروج من النظام المختلط...');

      // تنظيف المستخدم الحالي
      this.currentUser = null;

      // تسجيل خروج من Firebase
      await signOut(auth);
      console.log('✅ تم تسجيل الخروج من Firebase');

      // تسجيل خروج من Supabase
      await supabase.auth.signOut();
      console.log('✅ تم تسجيل الخروج من Supabase');

      // إشعار جميع المستمعين بتسجيل الخروج
      this.authListeners.forEach(listener => listener(null));

      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في تسجيل الخروج:', error);

      // حتى في حالة الخطأ، قم بتنظيف البيانات المحلية
      this.currentUser = null;
      this.authListeners.forEach(listener => listener(null));

      throw error;
    }
  }

  // إضافة مستمع للمصادقة
  onAuthStateChange(callback) {
    this.authListeners.add(callback);
    // إرجاع دالة إلغاء الاشتراك
    return () => this.authListeners.delete(callback);
  }

  // الحصول على المستخدم الحالي
  getCurrentUser() {
    return this.currentUser;
  }

  // الحصول على بيانات المستخدم من Supabase
  async getUserData() {
    if (!this.currentUser) return null;

    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('firebase_uid', this.currentUser.uid)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('❌ خطأ في جلب بيانات المستخدم:', error);
      return null;
    }
  }
}

/**
 * خدمة إدارة الكورسات المختلطة مع التزامن الفوري
 */
export class HybridCourseService {
  constructor() {
    this.realTimeSync = realTimeCoursesSync;
  }

  // مراقبة الكورسات مع التحديث الفوري
  watchCourses(callback) {
    return this.realTimeSync.watchCourses(callback);
  }

  // إضافة كورس جديد مع تحديث فوري
  async addCourse(courseData) {
    try {
      console.log('📚 إضافة كورس جديد مع تزامن فوري:', courseData.title);

      // إضافة في Firebase للتزامن الفوري
      const firebaseResult = await this.realTimeSync.addCourse({
        ...courseData,
        instructor: courseData.instructor || 'علاء عبد الحميد'
      });

      // إضافة في Supabase للتخزين المتقدم (اختياري)
      try {
        const { data, error } = await supabase
          .from('courses')
          .insert([{
            firebase_id: firebaseResult.id,
            ...courseData,
            instructor: 'علاء عبد الحميد',
            enrolled_students: 0,
            total_videos: 0,
            is_active: true
          }])
          .select()
          .single();

        if (error) {
          console.warn('⚠️ تحذير: فشل في مزامنة Supabase، لكن Firebase يعمل:', error);
        }
      } catch (supabaseError) {
        console.warn('⚠️ تحذير: فشل في مزامنة Supabase:', supabaseError);
      }

      console.log('✅ تم إضافة الكورس بنجاح مع تزامن فوري:', firebaseResult.id);
      return firebaseResult;
    } catch (error) {
      console.error('❌ خطأ في إضافة الكورس:', error);
      throw error;
    }
  }

  // جلب جميع الكورسات
  async getAllCourses() {
    try {
      const { data, error } = await supabase
        .from('courses')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map(course => ({
        id: course.id,
        ...course,
        createdAt: new Date(course.created_at),
        updatedAt: new Date(course.updated_at)
      }));
    } catch (error) {
      console.error('❌ خطأ في جلب الكورسات:', error);
      throw error;
    }
  }

  // تحديث كورس مع تزامن فوري
  async updateCourse(courseId, updateData) {
    try {
      console.log('🔄 تحديث الكورس مع تزامن فوري:', courseId);

      // تحديث في Firebase للتزامن الفوري
      const firebaseResult = await this.realTimeSync.updateCourse(courseId, updateData);

      // تحديث في Supabase (اختياري)
      try {
        const { data, error } = await supabase
          .from('courses')
          .update({
            ...updateData,
            updated_at: new Date().toISOString()
          })
          .eq('firebase_id', courseId)
          .select()
          .single();

        if (error) {
          console.warn('⚠️ تحذير: فشل في تحديث Supabase، لكن Firebase يعمل:', error);
        }
      } catch (supabaseError) {
        console.warn('⚠️ تحذير: فشل في تحديث Supabase:', supabaseError);
      }

      console.log('✅ تم تحديث الكورس بنجاح مع تزامن فوري');
      return firebaseResult;
    } catch (error) {
      console.error('❌ خطأ في تحديث الكورس:', error);
      throw error;
    }
  }

  // حذف كورس مع تزامن فوري
  async deleteCourse(courseId) {
    try {
      console.log('🗑️ حذف الكورس مع تزامن فوري:', courseId);

      // حذف من Firebase للتزامن الفوري
      const firebaseResult = await this.realTimeSync.deleteCourse(courseId);

      // حذف من Supabase (اختياري)
      try {
        const { error } = await supabase
          .from('courses')
          .delete()
          .eq('firebase_id', courseId);

        if (error) {
          console.warn('⚠️ تحذير: فشل في حذف من Supabase، لكن Firebase يعمل:', error);
        }
      } catch (supabaseError) {
        console.warn('⚠️ تحذير: فشل في حذف من Supabase:', supabaseError);
      }

      console.log('✅ تم حذف الكورس بنجاح مع تزامن فوري');
      return firebaseResult;
    } catch (error) {
      console.error('❌ خطأ في حذف الكورس:', error);
      throw error;
    }
  }

  // مراقبة الكورسات مع تحديثات فورية (تم نقلها للخدمة الجديدة)
  // watchCourses(callback) - تم تعريفها في constructor

  // إرسال إشعار للطلاب
  async notifyStudents(type, data) {
    try {
      // جلب جميع الطلاب النشطين
      const { data: students, error } = await supabase
        .from('users')
        .select('id')
        .eq('role', 'student')
        .eq('is_active', true);

      if (error) throw error;

      // إنشاء إشعارات للطلاب
      const notifications = students.map(student => ({
        user_id: student.id,
        title: this.getNotificationTitle(type),
        message: data.message,
        type: type,
        data: data
      }));

      await supabase
        .from('notifications')
        .insert(notifications);

      console.log(`🔔 تم إرسال إشعار ${type} لـ ${students.length} طالب`);
    } catch (error) {
      console.error('❌ خطأ في إرسال الإشعارات:', error);
    }
  }

  // الحصول على عنوان الإشعار
  getNotificationTitle(type) {
    const titles = {
      'new_course': 'كورس جديد متاح',
      'course_update': 'تحديث في الكورس',
      'enrollment': 'تم تسجيلك في كورس'
    };
    return titles[type] || 'إشعار جديد';
  }
}

/**
 * خدمة إدارة الطلاب المختلطة
 */
export class HybridStudentService {
  // إضافة طالب جديد مع تزامن Firebase و Supabase (مع fallback)
  async addStudent(studentData) {
    try {
      console.log('👨‍🎓 إضافة طالب جديد:', studentData.name);

      // توليد كود طالب فريد
      const studentCode = studentData.student_code || await this.generateUniqueStudentCodeFirebase();

      // بيانات الطالب الموحدة
      const studentRecord = {
        ...studentData,
        role: 'student',
        studentCode: studentCode,
        student_code: studentCode,
        isActive: true,
        is_active: true,
        enrolledCourses: 0,
        enrolled_courses: 0,
        totalProgress: 0,
        total_progress: 0,
        certificatesEarned: 0,
        certificates_earned: 0,
        createdAt: new Date().toISOString(),
        created_at: new Date().toISOString()
      };

      // إضافة في Firebase أولاً (الأساسي)
      let firebaseId = null;
      try {
        const firebaseDoc = await addDoc(collection(db, 'users'), {
          ...studentRecord,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
        firebaseId = firebaseDoc.id;
        console.log('✅ تم إضافة الطالب في Firebase:', firebaseId);
      } catch (firebaseError) {
        console.error('❌ خطأ في إضافة الطالب في Firebase:', firebaseError);
        throw new Error('فشل في إضافة الطالب في قاعدة البيانات الأساسية');
      }

      // محاولة إضافة في Supabase (اختياري)
      let supabaseData = null;
      try {
        const { data, error } = await supabase
          .from('users')
          .insert([{
            ...studentRecord,
            firebase_id: firebaseId
          }])
          .select()
          .single();

        if (error) throw error;
        supabaseData = data;
        console.log('✅ تم إضافة الطالب في Supabase:', data.id);
      } catch (supabaseError) {
        console.warn('⚠️ تحذير: فشل في إضافة الطالب في Supabase (سيعمل النظام بـ Firebase فقط):', supabaseError.message);
        // لا نحذف من Firebase - نتركه يعمل بـ Firebase فقط
      }

      console.log('✅ تم إضافة الطالب بنجاح');
      return {
        success: true,
        id: firebaseId,
        firebaseId: firebaseId,
        supabaseId: supabaseData?.id,
        studentCode: studentCode,
        data: { ...studentRecord, id: firebaseId }
      };
    } catch (error) {
      console.error('❌ خطأ في إضافة الطالب:', error);
      throw error;
    }
  }

  // توليد كود طالب فريد باستخدام Firebase
  async generateUniqueStudentCodeFirebase() {
    let isUnique = false;
    let code = '';

    while (!isUnique) {
      code = Math.floor(100000 + Math.random() * 900000).toString();

      try {
        // البحث في Firebase
        const q = query(
          collection(db, 'users'),
          where('studentCode', '==', code),
          limit(1)
        );
        const querySnapshot = await getDocs(q);

        if (querySnapshot.empty) {
          isUnique = true;
        }
      } catch (error) {
        console.warn('تحذير في البحث عن كود الطالب:', error);
        // في حالة الخطأ، نستخدم الكود الحالي
        isUnique = true;
      }
    }

    return code;
  }

  // توليد كود طالب فريد باستخدام Supabase (احتياطي)
  async generateUniqueStudentCode() {
    try {
      return await this.generateUniqueStudentCodeFirebase();
    } catch (error) {
      console.warn('فشل في توليد كود باستخدام Firebase، محاولة Supabase...');

      let isUnique = false;
      let code = '';

      while (!isUnique) {
        code = Math.floor(100000 + Math.random() * 900000).toString();

        try {
          const { data, error } = await supabase
            .from('users')
            .select('id')
            .eq('student_code', code)
            .limit(1);

          if (error) throw error;

          if (data.length === 0) {
            isUnique = true;
          }
        } catch (supabaseError) {
          console.warn('فشل في Supabase أيضاً، استخدام كود عشوائي');
          return Math.floor(100000 + Math.random() * 900000).toString();
        }
      }

      return code;
    }
  }

  // جلب جميع الطلاب
  async getAllStudents() {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('role', 'student')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map(student => ({
        id: student.id,
        ...student,
        createdAt: new Date(student.created_at),
        updatedAt: new Date(student.updated_at),
        lastLogin: student.last_login ? new Date(student.last_login) : null
      }));
    } catch (error) {
      console.error('❌ خطأ في جلب الطلاب:', error);
      throw error;
    }
  }

  // تحديث بيانات طالب
  async updateStudent(studentId, updateData) {
    try {
      const { data, error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', studentId)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ تم تحديث بيانات الطالب بنجاح');
      return { success: true, data };
    } catch (error) {
      console.error('❌ خطأ في تحديث الطالب:', error);
      throw error;
    }
  }

  // حذف طالب
  async deleteStudent(studentId) {
    try {
      // حذف جميع تسجيلات الطالب أولاً
      await supabase
        .from('enrollments')
        .delete()
        .eq('student_id', studentId);

      // حذف الطالب
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', studentId);

      if (error) throw error;

      console.log('✅ تم حذف الطالب وجميع تسجيلاته بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في حذف الطالب:', error);
      throw error;
    }
  }

  // مراقبة الطلاب مع تحديثات فورية (Firebase + Supabase)
  watchStudents(callback) {
    console.log('🔄 بدء مراقبة الطلاب...');

    // مراقبة Firebase للتحديثات الفورية
    const q = query(
      collection(db, 'users'),
      where('role', '==', 'student')
    );

    const unsubscribeFirebase = onSnapshot(q, (snapshot) => {
      const students = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
      }));

      // ترتيب الطلاب حسب تاريخ الإنشاء
      students.sort((a, b) => b.createdAt - a.createdAt);

      console.log('✅ تحديث فوري للطلاب من Firebase:', students.length);
      callback(students);
    }, (error) => {
      console.error('❌ خطأ في مراقبة الطلاب:', error);
      callback([]);
    });

    // محاولة إعداد مراقبة Supabase (اختياري)
    let supabaseSubscription = null;
    try {
      supabaseSubscription = supabase
        .channel('students_realtime')
        .on('postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'users',
            filter: 'role=eq.student'
          },
          () => {
            console.log('🔄 تحديث من Supabase - إعادة جلب البيانات');
            // لا نفعل شيء هنا لأن Firebase يتولى الأمر
          }
        )
        .subscribe();
    } catch (supabaseError) {
      console.warn('⚠️ تحذير: فشل في إعداد مراقبة Supabase للطلاب:', supabaseError);
    }

    return () => {
      console.log('🛑 إيقاف مراقبة الطلاب');
      unsubscribeFirebase();
      if (supabaseSubscription) {
        try {
          supabase.removeChannel(supabaseSubscription);
        } catch (error) {
          console.warn('تحذير في إغلاق Supabase subscription:', error);
        }
      }
    };
  }
}

/**
 * خدمة إدارة التسجيلات المختلطة
 */
export class HybridEnrollmentService {
  // تسجيل طالب في كورس
  async enrollStudent(studentId, courseId, enrollmentData = {}) {
    try {
      console.log('📝 تسجيل طالب في كورس:', { studentId, courseId });

      // التحقق من عدم وجود تسجيل سابق
      const { data: existing } = await supabase
        .from('enrollments')
        .select('id')
        .eq('student_id', studentId)
        .eq('course_id', courseId)
        .single();

      if (existing) {
        throw new Error('الطالب مسجل بالفعل في هذا الكورس');
      }

      // إضافة التسجيل
      const { data, error } = await supabase
        .from('enrollments')
        .insert([{
          student_id: studentId,
          course_id: courseId,
          status: 'active',
          progress: 0,
          completed_videos: [],
          ...enrollmentData
        }])
        .select()
        .single();

      if (error) throw error;

      // تحديث عدد الطلاب في الكورس
      await supabase.rpc('increment_course_students', {
        course_id: courseId
      });

      // إضافة إشعار للطالب
      await supabase
        .from('notifications')
        .insert([{
          user_id: studentId,
          title: 'تم تسجيلك في كورس جديد',
          message: 'تم تسجيلك بنجاح في الكورس',
          type: 'enrollment',
          data: { courseId, enrollmentId: data.id }
        }]);

      console.log('✅ تم تسجيل الطالب بنجاح');
      return { success: true, enrollmentId: data.id, data };
    } catch (error) {
      console.error('❌ خطأ في تسجيل الطالب:', error);
      throw error;
    }
  }

  // جلب جميع التسجيلات
  async getAllEnrollments() {
    try {
      const { data, error } = await supabase
        .from('enrollments')
        .select(`
          *,
          student:users!enrollments_student_id_fkey(id, name, email, student_code),
          course:courses!enrollments_course_id_fkey(id, title, instructor)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map(enrollment => ({
        id: enrollment.id,
        ...enrollment,
        createdAt: new Date(enrollment.created_at),
        updatedAt: new Date(enrollment.updated_at),
        lastAccessedAt: enrollment.last_accessed_at ? new Date(enrollment.last_accessed_at) : null
      }));
    } catch (error) {
      console.error('❌ خطأ في جلب التسجيلات:', error);
      throw error;
    }
  }

  // إلغاء تسجيل طالب من كورس
  async unenrollStudent(enrollmentId, courseId) {
    try {
      // حذف التسجيل
      const { error } = await supabase
        .from('enrollments')
        .delete()
        .eq('id', enrollmentId);

      if (error) throw error;

      // تقليل عدد الطلاب في الكورس
      await supabase.rpc('decrement_course_students', {
        course_id: courseId
      });

      console.log('✅ تم إلغاء تسجيل الطالب بنجاح');
      return { success: true };
    } catch (error) {
      console.error('❌ خطأ في إلغاء التسجيل:', error);
      throw error;
    }
  }

  // مراقبة التسجيلات مع تحديثات فورية
  watchEnrollments(callback) {
    console.log('🔄 بدء مراقبة التسجيلات...');

    this.getAllEnrollments().then(callback);

    const subscription = supabase
      .channel('enrollments_realtime')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'enrollments' },
        () => {
          this.getAllEnrollments().then(callback);
        }
      )
      .subscribe();

    return () => {
      console.log('🛑 إيقاف مراقبة التسجيلات');
      supabase.removeChannel(subscription);
    };
  }
}

/**
 * خدمة إدارة الأسئلة الشائعة المختلطة
 */
export class HybridFAQService {
  // إضافة سؤال شائع جديد
  async addFAQ(faqData) {
    try {
      console.log('❓ إضافة سؤال شائع جديد:', faqData.question);

      const { data, error } = await supabase
        .from('faqs')
        .insert([{
          ...faqData,
          is_active: true,
          views: 0,
          helpful_count: 0
        }])
        .select()
        .single();

      if (error) throw error;

      console.log('✅ تم إضافة السؤال الشائع بنجاح:', data.id);
      return { success: true, id: data.id, data };
    } catch (error) {
      console.error('❌ خطأ في إضافة السؤال الشائع:', error);
      throw error;
    }
  }

  // جلب جميع الأسئلة الشائعة
  async getAllFAQs() {
    try {
      const { data, error } = await supabase
        .from('faqs')
        .select('*')
        .eq('is_active', true)
        .order('priority', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map(faq => ({
        id: faq.id,
        ...faq,
        createdAt: new Date(faq.created_at),
        updatedAt: new Date(faq.updated_at)
      }));
    } catch (error) {
      console.error('❌ خطأ في جلب الأسئلة الشائعة:', error);
      throw error;
    }
  }

  // تحديث سؤال شائع
  async updateFAQ(faqId, updateData) {
    try {
      const { data, error } = await supabase
        .from('faqs')
        .update(updateData)
        .eq('id', faqId)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ تم تحديث السؤال الشائع بنجاح');
      return { success: true, data };
    } catch (error) {
      console.error('❌ خطأ في تحديث السؤال الشائع:', error);
      throw error;
    }
  }

  // حذف سؤال شائع (إلغاء تفعيل)
  async deleteFAQ(faqId) {
    try {
      const { data, error } = await supabase
        .from('faqs')
        .update({ is_active: false })
        .eq('id', faqId)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ تم حذف السؤال الشائع بنجاح');
      return { success: true, data };
    } catch (error) {
      console.error('❌ خطأ في حذف السؤال الشائع:', error);
      throw error;
    }
  }

  // مراقبة الأسئلة الشائعة مع تحديثات فورية
  watchFAQs(callback) {
    console.log('🔄 بدء مراقبة الأسئلة الشائعة...');

    this.getAllFAQs().then(callback);

    const subscription = supabase
      .channel('faqs_realtime')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'faqs' },
        () => {
          this.getAllFAQs().then(callback);
        }
      )
      .subscribe();

    return () => {
      console.log('🛑 إيقاف مراقبة الأسئلة الشائعة');
      supabase.removeChannel(subscription);
    };
  }
}

// إنشاء مثيلات الخدمات
export const hybridAuth = new HybridAuthService();
export const hybridCourses = new HybridCourseService();
export const hybridStudents = new HybridStudentService();
export const hybridEnrollments = new HybridEnrollmentService();
export const hybridFAQs = new HybridFAQService();
